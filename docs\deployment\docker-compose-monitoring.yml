# DL引擎监控服务 Docker Compose 配置
# 包含 Prometheus、<PERSON><PERSON>、ELK Stack 等监控组件
version: '3.8'

services:
  # ================================
  # 监控服务
  # ================================
  
  # 监控服务
  monitoring-service:
    build:
      context: ./server/monitoring-service
      dockerfile: Dockerfile
    container_name: dl-engine-monitoring-service
    restart: unless-stopped
    ports:
      - "3100:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=monitoring
      - ELASTICSEARCH_ENABLED=${ELASTICSEARCH_ENABLED}
      - ELASTICSEARCH_NODE=http://elasticsearch:9200
      - ELASTICSEARCH_INDEX=logs
      - SERVICE_REGISTRY_URL=http://service-registry:4010
      - MAIL_HOST=${MAIL_HOST}
      - MAIL_PORT=${MAIL_PORT}
      - MAIL_USER=${MAIL_USER}
      - MAIL_PASS=${MAIL_PASS}
      - MAIL_FROM=${MAIL_FROM}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - elasticsearch
    networks:
      - monitoring-network
      - dl-engine-backend
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # ================================
  # Prometheus 监控栈
  # ================================
  
  # Prometheus
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: dl-engine-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./config/prometheus/alert-rules.yml:/etc/prometheus/alert-rules.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Grafana
  grafana:
    image: grafana/grafana:10.0.0
    container_name: dl-engine-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
      - ./config/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Node Exporter
  node-exporter:
    image: prom/node-exporter:v1.6.0
    container_name: dl-engine-node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.ignored-mount-points=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # cAdvisor
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.0
    container_name: dl-engine-cadvisor
    restart: unless-stopped
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # ================================
  # ELK Stack 日志分析
  # ================================
  
  # Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: dl-engine-elasticsearch
    restart: unless-stopped
    environment:
      - node.name=elasticsearch
      - cluster.name=dl-engine-es
      - discovery.type=single-node
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - xpack.security.enabled=false
      - xpack.security.enrollment.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
      - "9300:9300"
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: dl-engine-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=kibana_password
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: dl-engine-logstash
    restart: unless-stopped
    environment:
      - xpack.monitoring.enabled=false
      - ELASTIC_USER=elastic
      - ELASTIC_PASSWORD=elastic_password
      - ELASTIC_HOSTS=http://elasticsearch:9200
    volumes:
      - ./config/logstash/pipeline:/usr/share/logstash/pipeline
      - ./config/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml
      - ./logs:/var/log/dl-engine:ro
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    depends_on:
      - elasticsearch
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: dl-engine-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./config/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - ./logs:/var/log/dl-engine:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - ELASTIC_USER=elastic
      - ELASTIC_PASSWORD=elastic_password
      - ELASTIC_HOSTS=elasticsearch:9200
      - KIBANA_HOSTS=kibana:5601
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # ================================
  # 告警管理
  # ================================
  
  # Alertmanager
  alertmanager:
    image: prom/alertmanager:v0.25.0
    container_name: dl-engine-alertmanager
    restart: unless-stopped
    volumes:
      - ./config/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    ports:
      - "9093:9093"
    networks:
      - monitoring-network
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # ================================
  # 数据库监控
  # ================================
  
  # MySQL Exporter
  mysql-exporter:
    image: prom/mysqld-exporter:v0.14.0
    container_name: dl-engine-mysql-exporter
    restart: unless-stopped
    environment:
      - DATA_SOURCE_NAME=root:${MYSQL_ROOT_PASSWORD}@(mysql:3306)/
    ports:
      - "9104:9104"
    depends_on:
      - mysql
    networks:
      - monitoring-network
      - dl-engine-database
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Redis Exporter
  redis-exporter:
    image: oliver006/redis_exporter:v1.51.0
    container_name: dl-engine-redis-exporter
    restart: unless-stopped
    environment:
      - REDIS_ADDR=redis:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    ports:
      - "9121:9121"
    depends_on:
      - redis
    networks:
      - monitoring-network
      - dl-engine-cache
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

# ================================
# 网络配置
# ================================
networks:
  monitoring-network:
    driver: bridge
    name: dl-engine-monitoring
  dl-engine-backend:
    external: true
  dl-engine-database:
    external: true
  dl-engine-cache:
    external: true

# ================================
# 数据卷配置
# ================================
volumes:
  prometheus-data:
    driver: local
    name: dl-engine-prometheus-data
  grafana-data:
    driver: local
    name: dl-engine-grafana-data
  elasticsearch-data:
    driver: local
    name: dl-engine-elasticsearch-data
  alertmanager-data:
    driver: local
    name: dl-engine-alertmanager-data
